version: '3.8'

services:
  # Redis pour le cache
  redis:
    image: redis:7-alpine
    container_name: nafaplace-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - nafaplace-network

  # Service Catalog (existant)
  catalog-api:
    build:
      context: .
      dockerfile: src/Services/Catalog/NafaPlace.Catalog.API/Dockerfile
    container_name: nafaplace-catalog-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=host.docker.internal;Database=NafaPlace.Catalog;Username=postgres;Password=NafaPlace2025@Dev
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=your-super-secret-key-here-make-it-long-and-complex
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlace
    ports:
      - "5243:80"
    depends_on:
      - redis
    networks:
      - nafaplace-network

  # Service Order (existant)
  order-api:
    build:
      context: .
      dockerfile: src/Services/Order/NafaPlace.Order.API/Dockerfile
    container_name: nafaplace-order-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=nafaplace_order;Username=nafaplace;Password=nafaplace123
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=your-super-secret-key-here-make-it-long-and-complex
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlace
      - Services__CatalogAPI=http://catalog-api
      - Services__NotificationAPI=http://notification-api
    ports:
      - "5004:80"
    depends_on:
      - postgres
      - redis
      - catalog-api
    networks:
      - nafaplace-network

  # Service Notification (existant)
  notification-api:
    build:
      context: .
      dockerfile: src/Services/Notification/NafaPlace.Notification.API/Dockerfile
    container_name: nafaplace-notification-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=nafaplace_notification;Username=nafaplace;Password=nafaplace123
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=your-super-secret-key-here-make-it-long-and-complex
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlace
    ports:
      - "5005:80"
    depends_on:
      - postgres
      - redis
    networks:
      - nafaplace-network

  # Service Analytics (nouveau)
  analytics-api:
    build:
      context: .
      dockerfile: src/Services/Analytics/NafaPlace.Analytics.API/Dockerfile
    container_name: nafaplace-analytics-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=nafaplace_analytics;Username=nafaplace;Password=nafaplace123
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=your-super-secret-key-here-make-it-long-and-complex
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlace
    ports:
      - "5006:80"
    depends_on:
      - postgres
      - redis
    networks:
      - nafaplace-network

  # Service Chat (nouveau)
  chat-api:
    build:
      context: .
      dockerfile: src/Services/Chat/NafaPlace.Chat.API/Dockerfile
    container_name: nafaplace-chat-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=nafaplace_chat;Username=nafaplace;Password=nafaplace123
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=your-super-secret-key-here-make-it-long-and-complex
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlace
    ports:
      - "5007:80"
    depends_on:
      - postgres
      - redis
    networks:
      - nafaplace-network

  # Service Recommendation (nouveau)
  recommendation-api:
    build:
      context: .
      dockerfile: src/Services/Recommendation/NafaPlace.Recommendation.API/Dockerfile
    container_name: nafaplace-recommendation-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=nafaplace_recommendation;Username=nafaplace;Password=nafaplace123
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=your-super-secret-key-here-make-it-long-and-complex
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlace
      - Services__CatalogAPI=http://catalog-api
      - Services__OrderAPI=http://order-api
    ports:
      - "5008:80"
    depends_on:
      - postgres
      - redis
      - catalog-api
    networks:
      - nafaplace-network

  # Service Loyalty (nouveau)
  loyalty-api:
    build:
      context: .
      dockerfile: src/Services/Loyalty/NafaPlace.Loyalty.API/Dockerfile
    container_name: nafaplace-loyalty-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=nafaplace_loyalty;Username=nafaplace;Password=nafaplace123
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=your-super-secret-key-here-make-it-long-and-complex
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlace
      - Services__OrderAPI=http://order-api
      - Services__NotificationAPI=http://notification-api
    ports:
      - "5009:80"
    depends_on:
      - postgres
      - redis
      - order-api
      - notification-api
    networks:
      - nafaplace-network

  # Service Localization (nouveau)
  localization-api:
    build:
      context: .
      dockerfile: src/Services/Localization/NafaPlace.Localization.API/Dockerfile
    container_name: nafaplace-localization-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=nafaplace_localization;Username=nafaplace;Password=nafaplace123
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=your-super-secret-key-here-make-it-long-and-complex
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlace
    ports:
      - "5010:80"
    depends_on:
      - postgres
      - redis
    networks:
      - nafaplace-network

  # Service Search (nouveau)
  search-api:
    build:
      context: .
      dockerfile: src/Services/Search/NafaPlace.Search.API/Dockerfile
    container_name: nafaplace-search-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=nafaplace_search;Username=nafaplace;Password=nafaplace123
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=your-super-secret-key-here-make-it-long-and-complex
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlace
      - Services__CatalogAPI=http://catalog-api
    ports:
      - "5011:80"
    depends_on:
      - postgres
      - redis
      - catalog-api
    networks:
      - nafaplace-network

  # Service Inventory (nouveau)
  inventory-api:
    build:
      context: .
      dockerfile: src/Services/Inventory/NafaPlace.Inventory.API/Dockerfile
    container_name: nafaplace-inventory-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=nafaplace_inventory;Username=nafaplace;Password=nafaplace123
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=your-super-secret-key-here-make-it-long-and-complex
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlace
      - Services__CatalogAPI=http://catalog-api
      - Services__OrderAPI=http://order-api
    ports:
      - "5012:80"
    depends_on:
      - postgres
      - redis
      - catalog-api
      - order-api
    networks:
      - nafaplace-network

  # API Gateway (existant)
  api-gateway:
    build:
      context: .
      dockerfile: src/ApiGateway/NafaPlace.ApiGateway/Dockerfile
    container_name: nafaplace-api-gateway
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - Services__CatalogAPI=http://catalog-api
      - Services__OrderAPI=http://order-api
      - Services__NotificationAPI=http://notification-api
      - Services__AnalyticsAPI=http://analytics-api
      - Services__ChatAPI=http://chat-api
      - Services__RecommendationAPI=http://recommendation-api
      - Services__LoyaltyAPI=http://loyalty-api
      - Services__LocalizationAPI=http://localization-api
      - Services__SearchAPI=http://search-api
      - Services__InventoryAPI=http://inventory-api
    ports:
      - "5000:80"
    depends_on:
      - catalog-api
      - order-api
      - notification-api
      - analytics-api
      - chat-api
      - recommendation-api
      - loyalty-api
      - localization-api
      - search-api
      - inventory-api
    networks:
      - nafaplace-network

  # Web App (existant)
  web-app:
    build:
      context: .
      dockerfile: src/Web/NafaPlace.Web.Server/Dockerfile
    container_name: nafaplace-web-app
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ApiGateway__BaseUrl=http://api-gateway
    ports:
      - "8080:80"
    depends_on:
      - api-gateway
    networks:
      - nafaplace-network

  # Admin Portal (existant)
  admin-portal:
    build:
      context: .
      dockerfile: src/Web/NafaPlace.Admin.Server/Dockerfile
    container_name: nafaplace-admin-portal
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ApiGateway__BaseUrl=http://api-gateway
    ports:
      - "8081:80"
    depends_on:
      - api-gateway
    networks:
      - nafaplace-network

  # Seller Portal (existant)
  seller-portal:
    build:
      context: .
      dockerfile: src/Web/NafaPlace.Seller.Server/Dockerfile
    container_name: nafaplace-seller-portal
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ApiGateway__BaseUrl=http://api-gateway
    ports:
      - "8082:80"
    depends_on:
      - api-gateway
    networks:
      - nafaplace-network

volumes:
  postgres_data:
  redis_data:

networks:
  nafaplace-network:
    driver: bridge
