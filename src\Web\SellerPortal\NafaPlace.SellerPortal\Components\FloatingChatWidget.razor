@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider

<!-- Chat Widget Flottant -->
<div class="floating-chat-widget">
    <!-- Bouton Chat Flottant -->
    <div class="chat-toggle-btn @(isOpen ? "hidden" : "")" @onclick="ToggleChat">
        <i class="bi bi-chat-dots"></i>
        @if (unreadCount > 0)
        {
            <span class="notification-badge">@unreadCount</span>
        }
    </div>

    <!-- Fen<PERSON><PERSON> de Chat -->
    <div class="chat-window @(isOpen ? "open" : "")">
        <!-- Header du Chat -->
        <div class="chat-header">
            <div class="d-flex align-items-center">
                <div class="chat-avatar">
                    <i class="bi bi-headset"></i>
                </div>
                <div class="chat-info">
                    <h6 class="mb-0">Support Vendeur</h6>
                    <small class="status-text">
                        <span class="status-dot online"></span>
                        En ligne
                    </small>
                </div>
            </div>
            <div class="chat-actions">
                <button class="btn-minimize" @onclick="MinimizeChat">
                    <i class="bi bi-dash"></i>
                </button>
                <button class="btn-close-chat" @onclick="CloseChat">
                    <i class="bi bi-x"></i>
                </button>
            </div>
        </div>

        <!-- Messages du Chat -->
        <div class="chat-messages" id="chatMessages">
            @if (messages.Any())
            {
                @foreach (var message in messages)
                {
                    <div class="message @(message.IsFromUser ? "user" : "support")">
                        @if (!message.IsFromUser)
                        {
                            <div class="message-avatar">
                                <i class="bi bi-person-circle"></i>
                            </div>
                        }
                        <div class="message-content">
                            <div class="message-bubble">
                                @message.Content
                            </div>
                            <div class="message-time">
                                @message.Timestamp.ToString("HH:mm")
                            </div>
                        </div>
                    </div>
                }
            }
            else
            {
                <div class="welcome-message">
                    <div class="welcome-icon">
                        <i class="bi bi-chat-heart"></i>
                    </div>
                    <h6>Support Vendeur</h6>
                    <p>Comment pouvons-nous vous aider ?</p>
                </div>
            }

            @if (isTyping)
            {
                <div class="message support">
                    <div class="message-avatar">
                        <i class="bi bi-person-circle"></i>
                    </div>
                    <div class="message-content">
                        <div class="message-bubble typing">
                            <div class="typing-indicator">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>

        <!-- Suggestions Rapides -->
        @if (!messages.Any() && !isTyping)
        {
            <div class="quick-suggestions">
                @foreach (var suggestion in quickSuggestions)
                {
                    <button class="suggestion-btn" @onclick="() => SendQuickMessage(suggestion)">
                        @suggestion
                    </button>
                }
            </div>
        }

        <!-- Zone de Saisie -->
        <div class="chat-input">
            <div class="input-group">
                <input type="text" class="form-control" 
                       placeholder="Tapez votre message..." 
                       @bind="currentMessage" 
                       @onkeypress="HandleKeyPress"
                       disabled="@isLoading">
                <button class="btn btn-primary" @onclick="SendMessage" 
                        disabled="@(isLoading || string.IsNullOrWhiteSpace(currentMessage))">
                    @if (isLoading)
                    {
                        <span class="spinner-border spinner-border-sm"></span>
                    }
                    else
                    {
                        <i class="bi bi-send"></i>
                    }
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    .floating-chat-widget {
        position: fixed !important;
        bottom: 20px !important;
        right: 20px !important;
        z-index: 99999 !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        pointer-events: auto !important;
    }

    .chat-toggle-btn {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #E73C30, #F96302);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
        cursor: pointer;
        box-shadow: 0 4px 20px rgba(231, 60, 48, 0.3);
        transition: all 0.3s ease;
        position: relative;
        animation: pulse 2s infinite;
    }

    .chat-toggle-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 25px rgba(231, 60, 48, 0.4);
    }

    .chat-toggle-btn.hidden {
        opacity: 0;
        transform: scale(0);
        pointer-events: none;
    }

    .notification-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #dc3545;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }

    @@keyframes pulse {
        0% { box-shadow: 0 4px 20px rgba(231, 60, 48, 0.3); }
        50% { box-shadow: 0 4px 20px rgba(231, 60, 48, 0.6); }
        100% { box-shadow: 0 4px 20px rgba(231, 60, 48, 0.3); }
    }

    .chat-window {
        width: 320px;
        height: 450px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
        display: flex;
        flex-direction: column;
        transform: translateY(100%) scale(0.8);
        opacity: 0;
        transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        pointer-events: none;
        overflow: hidden;
    }

    .chat-window.open {
        transform: translateY(0) scale(1);
        opacity: 1;
        pointer-events: all;
    }

    .chat-header {
        background: linear-gradient(135deg, #E73C30, #F96302);
        color: white;
        padding: 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .chat-avatar {
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        font-size: 18px;
    }

    .chat-info h6 {
        margin: 0;
        font-weight: 600;
    }

    .status-text {
        opacity: 0.9;
        font-size: 12px;
    }

    .status-dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 5px;
    }

    .status-dot.online {
        background: #28a745;
        animation: blink 1.5s infinite;
    }

    @@keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0.3; }
    }

    .chat-actions {
        display: flex;
        gap: 5px;
    }

    .btn-minimize, .btn-close-chat {
        background: none;
        border: none;
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background 0.2s;
    }

    .btn-minimize:hover, .btn-close-chat:hover {
        background: rgba(255, 255, 255, 0.2);
    }

    .chat-messages {
        flex: 1;
        padding: 15px;
        overflow-y: auto;
        background: #f8f9fa;
    }

    .message {
        display: flex;
        margin-bottom: 15px;
        align-items: flex-end;
    }

    .message.user {
        justify-content: flex-end;
    }

    .message-avatar {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: #6c757d;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
        font-size: 14px;
        flex-shrink: 0;
    }

    .message-content {
        max-width: 70%;
    }

    .message-bubble {
        padding: 10px 15px;
        border-radius: 18px;
        font-size: 14px;
        line-height: 1.4;
    }

    .message.support .message-bubble {
        background: white;
        color: #333;
        border-bottom-left-radius: 5px;
    }

    .message.user .message-bubble {
        background: linear-gradient(135deg, #E73C30, #F96302);
        color: white;
        border-bottom-right-radius: 5px;
    }

    .message-time {
        font-size: 11px;
        color: #6c757d;
        margin-top: 5px;
        text-align: right;
    }

    .message.support .message-time {
        text-align: left;
    }

    .welcome-message {
        text-align: center;
        padding: 30px 20px;
        color: #6c757d;
    }

    .welcome-icon {
        font-size: 48px;
        color: #E73C30;
        margin-bottom: 15px;
    }

    .typing-indicator {
        display: flex;
        gap: 3px;
        padding: 8px 0;
    }

    .typing-indicator span {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #6c757d;
        animation: typing 1.4s infinite ease-in-out;
    }

    .typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
    .typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

    @@keyframes typing {
        0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
        40% { transform: scale(1); opacity: 1; }
    }

    .quick-suggestions {
        padding: 10px 15px;
        border-top: 1px solid #e9ecef;
        background: white;
    }

    .suggestion-btn {
        display: block;
        width: 100%;
        background: none;
        border: 1px solid #e9ecef;
        padding: 8px 12px;
        margin-bottom: 5px;
        border-radius: 20px;
        font-size: 13px;
        color: #6c757d;
        cursor: pointer;
        transition: all 0.2s;
        text-align: left;
    }

    .suggestion-btn:hover {
        background: #f8f9fa;
        border-color: #E73C30;
        color: #E73C30;
    }

    .chat-input {
        padding: 15px;
        border-top: 1px solid #e9ecef;
        background: white;
    }

    .chat-input .form-control {
        border: 1px solid #e9ecef;
        border-radius: 25px;
        padding: 10px 15px;
        font-size: 14px;
    }

    .chat-input .btn {
        border-radius: 50%;
        width: 40px;
        height: 40px;
        padding: 0;
        margin-left: 8px;
    }

    /* Responsive */
    @@media (max-width: 768px) {
        .floating-chat-widget {
            bottom: 15px;
            right: 15px;
        }
        
        .chat-window {
            width: calc(100vw - 30px);
            height: calc(100vh - 100px);
            max-width: 320px;
            max-height: 450px;
        }
    }
</style>

@code {
    private bool isOpen = false;
    private bool isMinimized = false;
    private bool isLoading = false;
    private bool isTyping = false;
    private string currentMessage = "";
    private int unreadCount = 0;
    private List<ChatMessage> messages = new();
    private Timer? autoShowTimer;
    
    private List<string> quickSuggestions = new()
    {
        "Gestion produits",
        "Commandes en attente", 
        "Problème paiement",
        "Support technique",
        "Formation vendeur"
    };

    protected override async Task OnInitializedAsync()
    {
        StartAutoShowTimer();
        await Task.Delay(2000);
        if (!isOpen)
        {
            unreadCount = 1;
            StateHasChanged();
        }
    }

    private void StartAutoShowTimer()
    {
        autoShowTimer = new Timer(async _ =>
        {
            if (!isOpen && !isMinimized)
            {
                unreadCount++;
                await InvokeAsync(StateHasChanged);
            }
        }, null, TimeSpan.FromMinutes(3), TimeSpan.FromMinutes(3));
    }

    private void ToggleChat()
    {
        isOpen = !isOpen;
        isMinimized = false;
        
        if (isOpen)
        {
            unreadCount = 0;
            if (!messages.Any())
            {
                _ = Task.Run(async () =>
                {
                    await Task.Delay(1000);
                    await AddSupportMessage("Bonjour ! Support vendeur à votre service.", "Support Vendeur");
                });
            }
        }
        
        StateHasChanged();
    }

    private void MinimizeChat()
    {
        isOpen = false;
        isMinimized = true;
        StateHasChanged();
    }

    private void CloseChat()
    {
        isOpen = false;
        isMinimized = false;
        StateHasChanged();
    }

    private async Task HandleKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter" && !string.IsNullOrWhiteSpace(currentMessage))
        {
            await SendMessage();
        }
    }

    private async Task SendMessage()
    {
        if (string.IsNullOrWhiteSpace(currentMessage) || isLoading) return;

        var message = currentMessage.Trim();
        currentMessage = "";
        isLoading = true;

        messages.Add(new ChatMessage
        {
            Content = message,
            IsFromUser = true,
            Timestamp = DateTime.Now,
            SenderName = "Vous"
        });

        StateHasChanged();
        await ScrollToBottom();

        isTyping = true;
        StateHasChanged();

        await Task.Delay(1500);

        isTyping = false;
        var response = GenerateResponse(message);
        await AddSupportMessage(response, "Support Vendeur");

        isLoading = false;
        StateHasChanged();
    }

    private async Task SendQuickMessage(string message)
    {
        currentMessage = message;
        await SendMessage();
    }

    private async Task AddSupportMessage(string content, string senderName)
    {
        messages.Add(new ChatMessage
        {
            Content = content,
            IsFromUser = false,
            Timestamp = DateTime.Now,
            SenderName = senderName
        });

        StateHasChanged();
        await ScrollToBottom();
    }

    private string GenerateResponse(string userMessage)
    {
        var lowerMessage = userMessage.ToLower();
        
        if (lowerMessage.Contains("produit") || lowerMessage.Contains("gestion"))
            return "Pour gérer vos produits, allez dans 'Inventaire' > 'Mes Produits'.";
        
        if (lowerMessage.Contains("commande") || lowerMessage.Contains("attente"))
            return "Consultez vos commandes en attente dans 'Commandes' > 'En cours'.";
        
        if (lowerMessage.Contains("paiement") || lowerMessage.Contains("argent"))
            return "Pour les questions de paiement, contactez notre équipe financière.";
        
        if (lowerMessage.Contains("technique") || lowerMessage.Contains("problème"))
            return "Décrivez votre problème technique. Notre équipe vous assistera.";
        
        if (lowerMessage.Contains("bonjour") || lowerMessage.Contains("salut"))
            return "Bonjour ! Comment puis-je vous aider avec votre boutique ?";
        
        return "Merci pour votre message. Un conseiller vendeur va vous répondre rapidement !";
    }

    private async Task ScrollToBottom()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("scrollToBottom", "chatMessages");
        }
        catch { }
    }

    public void Dispose()
    {
        autoShowTimer?.Dispose();
    }

    public class ChatMessage
    {
        public string Content { get; set; } = "";
        public bool IsFromUser { get; set; }
        public DateTime Timestamp { get; set; }
        public string SenderName { get; set; } = "";
    }
}
