// Floating Chat Widget JavaScript
window.floatingChat = {
    // Fonction pour s'assurer que le widget est bien positionné
    ensureFloatingPosition: function() {
        const widget = document.querySelector('.floating-chat-widget');
        if (widget) {
            // Forcer le positionnement fixe
            widget.style.position = 'fixed';
            widget.style.bottom = '20px';
            widget.style.right = '20px';
            widget.style.zIndex = '99999';
            widget.style.pointerEvents = 'auto';
            widget.style.display = 'block';
            widget.style.visibility = 'visible';
            
            // S'assurer que le widget est attaché au body
            if (widget.parentElement !== document.body) {
                document.body.appendChild(widget);
            }
        }
    },

    // Fonction pour déplacer le widget vers le body
    moveToBody: function() {
        const widget = document.querySelector('.floating-chat-widget');
        if (widget && widget.parentElement !== document.body) {
            // Détacher du parent actuel et attacher au body
            widget.remove();
            document.body.appendChild(widget);
            
            // Appliquer les styles de positionnement
            this.ensureFloatingPosition();
        }
    },

    // Observer les changements DOM pour maintenir la position
    observeChanges: function() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    // Vérifier si le widget existe et le repositionner si nécessaire
                    setTimeout(() => {
                        this.ensureFloatingPosition();
                    }, 100);
                }
            });
        });

        // Observer les changements dans le document
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    },

    // Initialiser le widget flottant
    init: function() {
        // Attendre que le DOM soit prêt
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupWidget();
            });
        } else {
            this.setupWidget();
        }
    },

    setupWidget: function() {
        // Déplacer le widget vers le body
        this.moveToBody();
        
        // Observer les changements
        this.observeChanges();
        
        // Vérifier périodiquement la position
        setInterval(() => {
            this.ensureFloatingPosition();
        }, 5000);
    }
};

// Fonction pour faire défiler vers le bas dans le chat
window.scrollToBottom = function(elementId) {
    const element = document.getElementById(elementId) || document.querySelector('.chat-messages');
    if (element) {
        element.scrollTop = element.scrollHeight;
    }
};

// Initialiser automatiquement
window.floatingChat.init();
