// Floating Chat Widget JavaScript
window.floatingChat = {
    widgetHtml: null,
    isOpen: false,

    // C<PERSON>er le widget HTML directement
    createWidget: function() {
        if (document.getElementById('fixed-chat-widget')) {
            return; // Widget déjà créé
        }

        const widgetHtml = `
            <div id="fixed-chat-widget" class="fixed-chat-widget">
                <div class="chat-toggle-btn" onclick="window.floatingChat.toggleChat()">
                    <i class="bi bi-chat-dots"></i>
                    <div class="notification-badge" style="display: none;">1</div>
                </div>

                <div class="chat-window" id="chat-window">
                    <div class="chat-header">
                        <div class="chat-avatar">
                            <i class="bi bi-headset"></i>
                        </div>
                        <div class="chat-info">
                            <h6>Support NafaPlace</h6>
                            <div class="status-text">
                                <span class="status-dot online"></span>En ligne
                            </div>
                        </div>
                        <div class="chat-actions">
                            <button class="btn-minimize" onclick="window.floatingChat.minimizeChat()">
                                <i class="bi bi-dash"></i>
                            </button>
                            <button class="btn-close-chat" onclick="window.floatingChat.closeChat()">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    </div>

                    <div class="chat-messages" id="chat-messages">
                        <div class="welcome-message">
                            <div class="welcome-icon">
                                <i class="bi bi-chat-heart"></i>
                            </div>
                            <h6>Bienvenue !</h6>
                            <p>Comment puis-je vous aider aujourd'hui ?</p>
                        </div>
                    </div>

                    <div class="quick-suggestions" id="quick-suggestions">
                        <button class="suggestion-btn" onclick="window.floatingChat.sendQuickMessage('Suivi de commande')">Suivi de commande</button>
                        <button class="suggestion-btn" onclick="window.floatingChat.sendQuickMessage('Retour produit')">Retour produit</button>
                        <button class="suggestion-btn" onclick="window.floatingChat.sendQuickMessage('Problème de paiement')">Problème de paiement</button>
                        <button class="suggestion-btn" onclick="window.floatingChat.sendQuickMessage('Livraison')">Livraison</button>
                    </div>

                    <div class="chat-input">
                        <div class="input-group">
                            <input type="text" class="form-control" id="chat-input-field"
                                   placeholder="Tapez votre message..."
                                   onkeypress="window.floatingChat.handleKeyPress(event)">
                            <button class="btn btn-primary" onclick="window.floatingChat.sendMessage()">
                                <i class="bi bi-send"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Injecter directement dans le body
        document.body.insertAdjacentHTML('beforeend', widgetHtml);

        // Ajouter les styles CSS
        this.addStyles();

        console.log('Widget de chat créé et injecté dans le body');
    },

    // Fonction pour s'assurer que le widget est bien positionné
    ensureFloatingPosition: function() {
        const widget = document.getElementById('fixed-chat-widget');
        if (widget) {
            // Forcer le positionnement fixe
            widget.style.setProperty('position', 'fixed', 'important');
            widget.style.setProperty('bottom', '20px', 'important');
            widget.style.setProperty('right', '20px', 'important');
            widget.style.setProperty('z-index', '2147483647', 'important');
            widget.style.setProperty('pointer-events', 'auto', 'important');
            widget.style.setProperty('display', 'block', 'important');
            widget.style.setProperty('visibility', 'visible', 'important');
            widget.style.setProperty('opacity', '1', 'important');

            console.log('Position du chat vérifiée');
        } else {
            // Si le widget n'existe pas, le créer
            this.createWidget();
        }
    },

    // Ajouter les styles CSS
    addStyles: function() {
        if (document.getElementById('chat-widget-styles')) {
            return; // Styles déjà ajoutés
        }

        const styles = `
            <style id="chat-widget-styles">
                .fixed-chat-widget {
                    position: fixed !important;
                    bottom: 20px !important;
                    right: 20px !important;
                    z-index: 2147483647 !important;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                    pointer-events: auto !important;
                    display: block !important;
                    visibility: visible !important;
                }

                .chat-toggle-btn {
                    width: 60px;
                    height: 60px;
                    background: linear-gradient(135deg, #E73C30, #F96302);
                    border-radius: 50%;
                    display: flex !important;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 24px;
                    cursor: pointer;
                    box-shadow: 0 4px 20px rgba(231, 60, 48, 0.3);
                    transition: all 0.3s ease;
                    position: relative;
                    animation: pulse 2s infinite;
                    border: none;
                }

                .chat-toggle-btn:hover {
                    transform: scale(1.1);
                    box-shadow: 0 6px 25px rgba(231, 60, 48, 0.4);
                }

                .notification-badge {
                    position: absolute;
                    top: -5px;
                    right: -5px;
                    background: #dc3545;
                    color: white;
                    border-radius: 50%;
                    width: 20px;
                    height: 20px;
                    font-size: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: bold;
                }

                @keyframes pulse {
                    0% { box-shadow: 0 4px 20px rgba(231, 60, 48, 0.3); }
                    50% { box-shadow: 0 4px 20px rgba(231, 60, 48, 0.6); }
                    100% { box-shadow: 0 4px 20px rgba(231, 60, 48, 0.3); }
                }

                .chat-window {
                    width: 320px;
                    height: 450px;
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
                    display: flex;
                    flex-direction: column;
                    transform: translateY(100%) scale(0.8);
                    opacity: 0;
                    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
                    pointer-events: none;
                    overflow: hidden;
                    position: absolute;
                    bottom: 80px;
                    right: 0;
                }

                .chat-window.open {
                    transform: translateY(0) scale(1);
                    opacity: 1;
                    pointer-events: all;
                }

                .chat-header {
                    background: linear-gradient(135deg, #E73C30, #F96302);
                    color: white;
                    padding: 15px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                .chat-avatar {
                    width: 40px;
                    height: 40px;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 10px;
                    font-size: 18px;
                }

                .chat-info h6 {
                    margin: 0;
                    font-weight: 600;
                }

                .status-text {
                    opacity: 0.9;
                    font-size: 12px;
                }

                .status-dot {
                    display: inline-block;
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    margin-right: 5px;
                }

                .status-dot.online {
                    background: #28a745;
                    animation: blink 1.5s infinite;
                }

                @keyframes blink {
                    0%, 50% { opacity: 1; }
                    51%, 100% { opacity: 0.3; }
                }

                .chat-actions {
                    display: flex;
                    gap: 5px;
                }

                .btn-minimize, .btn-close-chat {
                    background: none;
                    border: none;
                    color: white;
                    width: 30px;
                    height: 30px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    transition: background 0.2s;
                }

                .btn-minimize:hover, .btn-close-chat:hover {
                    background: rgba(255, 255, 255, 0.2);
                }

                .chat-messages {
                    flex: 1;
                    padding: 15px;
                    overflow-y: auto;
                    background: #f8f9fa;
                }

                .welcome-message {
                    text-align: center;
                    padding: 30px 20px;
                    color: #6c757d;
                }

                .welcome-icon {
                    font-size: 48px;
                    color: #E73C30;
                    margin-bottom: 15px;
                }

                .quick-suggestions {
                    padding: 10px 15px;
                    border-top: 1px solid #e9ecef;
                    background: white;
                }

                .suggestion-btn {
                    display: block;
                    width: 100%;
                    background: none;
                    border: 1px solid #e9ecef;
                    padding: 8px 12px;
                    margin-bottom: 5px;
                    border-radius: 20px;
                    font-size: 13px;
                    color: #6c757d;
                    cursor: pointer;
                    transition: all 0.2s;
                    text-align: left;
                }

                .suggestion-btn:hover {
                    background: #f8f9fa;
                    border-color: #E73C30;
                    color: #E73C30;
                }

                .chat-input {
                    padding: 15px;
                    border-top: 1px solid #e9ecef;
                    background: white;
                }

                .input-group {
                    display: flex;
                    gap: 8px;
                }

                .chat-input .form-control {
                    border: 1px solid #e9ecef;
                    border-radius: 25px;
                    padding: 10px 15px;
                    font-size: 14px;
                    flex: 1;
                }

                .chat-input .btn {
                    border-radius: 50%;
                    width: 40px;
                    height: 40px;
                    padding: 0;
                    background: linear-gradient(135deg, #E73C30, #F96302);
                    border: none;
                    color: white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                }

                .message {
                    display: flex;
                    margin-bottom: 15px;
                    align-items: flex-end;
                }

                .message.user {
                    justify-content: flex-end;
                }

                .message-avatar {
                    width: 30px;
                    height: 30px;
                    border-radius: 50%;
                    background: #6c757d;
                    color: white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 8px;
                    font-size: 14px;
                    flex-shrink: 0;
                }

                .message.user .message-avatar {
                    margin-right: 0;
                    margin-left: 8px;
                    background: linear-gradient(135deg, #E73C30, #F96302);
                }

                .message-content {
                    max-width: 70%;
                }

                .message-bubble {
                    padding: 10px 15px;
                    border-radius: 18px;
                    font-size: 14px;
                    line-height: 1.4;
                }

                .message.support .message-bubble {
                    background: white;
                    color: #333;
                    border-bottom-left-radius: 5px;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                }

                .message.user .message-bubble {
                    background: linear-gradient(135deg, #E73C30, #F96302);
                    color: white;
                    border-bottom-right-radius: 5px;
                }

                .message-time {
                    font-size: 11px;
                    color: #6c757d;
                    margin-top: 5px;
                    text-align: right;
                }

                .message.support .message-time {
                    text-align: left;
                }

                @media (max-width: 768px) {
                    .fixed-chat-widget {
                        bottom: 15px !important;
                        right: 15px !important;
                    }

                    .chat-window {
                        width: calc(100vw - 30px);
                        height: calc(100vh - 100px);
                        max-width: 320px;
                        max-height: 450px;
                    }

                    .chat-toggle-btn {
                        width: 50px !important;
                        height: 50px !important;
                        font-size: 20px !important;
                    }
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', styles);
    },

    // Fonctions d'interaction du chat
    toggleChat: function() {
        const chatWindow = document.getElementById('chat-window');
        const badge = document.querySelector('.notification-badge');

        if (chatWindow) {
            this.isOpen = !this.isOpen;

            if (this.isOpen) {
                chatWindow.classList.add('open');
                if (badge) badge.style.display = 'none';

                // Ajouter message de bienvenue si premier ouverture
                setTimeout(() => {
                    this.addMessage('Bonjour ! Comment puis-je vous aider aujourd\'hui ?', false);
                }, 1000);
            } else {
                chatWindow.classList.remove('open');
            }
        }
    },

    minimizeChat: function() {
        const chatWindow = document.getElementById('chat-window');
        if (chatWindow) {
            chatWindow.classList.remove('open');
            this.isOpen = false;
        }
    },

    closeChat: function() {
        const chatWindow = document.getElementById('chat-window');
        if (chatWindow) {
            chatWindow.classList.remove('open');
            this.isOpen = false;
        }
    },

    handleKeyPress: function(event) {
        if (event.key === 'Enter') {
            this.sendMessage();
        }
    },

    sendMessage: function() {
        const input = document.getElementById('chat-input-field');
        if (input && input.value.trim()) {
            const message = input.value.trim();
            input.value = '';

            // Ajouter le message de l'utilisateur
            this.addMessage(message, true);

            // Simuler une réponse après un délai
            setTimeout(() => {
                const response = this.generateResponse(message);
                this.addMessage(response, false);
            }, 1500);
        }
    },

    sendQuickMessage: function(message) {
        const input = document.getElementById('chat-input-field');
        if (input) {
            input.value = message;
            this.sendMessage();
        }
    },

    addMessage: function(content, isUser) {
        const messagesContainer = document.getElementById('chat-messages');
        const suggestionsContainer = document.getElementById('quick-suggestions');

        if (messagesContainer) {
            // Supprimer le message de bienvenue s'il existe
            const welcomeMessage = messagesContainer.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.remove();
            }

            // Masquer les suggestions après le premier message
            if (suggestionsContainer && messagesContainer.children.length === 0) {
                suggestionsContainer.style.display = 'none';
            }

            const messageHtml = `
                <div class="message ${isUser ? 'user' : 'support'}">
                    ${!isUser ? '<div class="message-avatar"><i class="bi bi-person-circle"></i></div>' : ''}
                    <div class="message-content">
                        <div class="message-bubble">
                            ${content}
                        </div>
                        <div class="message-time">
                            ${new Date().toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                        </div>
                    </div>
                    ${isUser ? '<div class="message-avatar"><i class="bi bi-person-fill"></i></div>' : ''}
                </div>
            `;

            messagesContainer.insertAdjacentHTML('beforeend', messageHtml);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    },

    generateResponse: function(userMessage) {
        const lowerMessage = userMessage.toLowerCase();

        if (lowerMessage.includes('commande') || lowerMessage.includes('suivi')) {
            return 'Pour suivre votre commande, rendez-vous dans votre espace client > Mes Commandes.';
        }

        if (lowerMessage.includes('retour') || lowerMessage.includes('remboursement')) {
            return 'Vous pouvez retourner un produit dans les 14 jours. Allez dans "Mes Commandes" > "Retourner".';
        }

        if (lowerMessage.includes('paiement') || lowerMessage.includes('carte')) {
            return 'Nous acceptons Visa/Mastercard, Orange Money, et paiement à la livraison.';
        }

        if (lowerMessage.includes('livraison') || lowerMessage.includes('délai')) {
            return 'Délais de livraison : 2-5 jours ouvrables. Gratuit dès 50.000 GNF.';
        }

        if (lowerMessage.includes('bonjour') || lowerMessage.includes('salut')) {
            return 'Bonjour ! Je suis ravi de vous aider. Quelle est votre question ?';
        }

        return 'Merci pour votre message. Un agent va vous répondre rapidement !';
    },

    // Observer les changements DOM pour maintenir la position
    observeChanges: function() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    // Vérifier si le widget existe et le repositionner si nécessaire
                    setTimeout(() => {
                        this.ensureFloatingPosition();
                    }, 100);
                }
            });
        });

        // Observer les changements dans le document
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    },

    // Initialiser le widget flottant
    init: function() {
        // Attendre que le DOM soit prêt
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => this.setupWidget(), 500);
            });
        } else {
            setTimeout(() => this.setupWidget(), 500);
        }
    }

    setupWidget: function() {
        // Supprimer tout widget Blazor existant pour éviter les conflits
        const existingWidgets = document.querySelectorAll('.floating-chat-widget');
        existingWidgets.forEach(widget => widget.remove());

        // Créer notre widget JavaScript pur
        this.createWidget();

        // Observer les changements
        this.observeChanges();

        // Vérifier périodiquement la position
        setInterval(() => {
            this.ensureFloatingPosition();
        }, 1000);

        // Vérifier lors des événements de scroll et resize
        window.addEventListener('scroll', () => {
            this.ensureFloatingPosition();
        });

        window.addEventListener('resize', () => {
            this.ensureFloatingPosition();
        });

        console.log('Widget de chat configuré avec succès');
    }
};

// Fonction pour faire défiler vers le bas dans le chat
window.scrollToBottom = function(elementId) {
    const element = document.getElementById(elementId) || document.querySelector('.chat-messages');
    if (element) {
        element.scrollTop = element.scrollHeight;
    }
};

// Initialiser automatiquement
window.floatingChat.init();
