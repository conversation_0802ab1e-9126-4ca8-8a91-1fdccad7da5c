// Floating Chat Widget JavaScript
window.floatingChat = {
    // Fonction pour s'assurer que le widget est bien positionné
    ensureFloatingPosition: function() {
        const widget = document.querySelector('.floating-chat-widget') || document.getElementById('floating-chat-widget');
        if (widget) {
            // Déplacer vers le body si ce n'est pas déjà fait
            if (widget.parentElement !== document.body) {
                const clonedWidget = widget.cloneNode(true);
                widget.remove();
                document.body.appendChild(clonedWidget);
            }

            // Récupérer le widget dans le body
            const bodyWidget = document.body.querySelector('.floating-chat-widget');
            if (bodyWidget) {
                // Forcer tous les styles de positionnement
                bodyWidget.style.setProperty('position', 'fixed', 'important');
                bodyWidget.style.setProperty('bottom', '20px', 'important');
                bodyWidget.style.setProperty('right', '20px', 'important');
                bodyWidget.style.setProperty('z-index', '2147483647', 'important');
                bodyWidget.style.setProperty('pointer-events', 'auto', 'important');
                bodyWidget.style.setProperty('display', 'block', 'important');
                bodyWidget.style.setProperty('visibility', 'visible', 'important');
                bodyWidget.style.setProperty('opacity', '1', 'important');
                bodyWidget.style.setProperty('transform', 'none', 'important');
                bodyWidget.style.setProperty('margin', '0', 'important');
                bodyWidget.style.setProperty('padding', '0', 'important');
                bodyWidget.style.setProperty('top', 'auto', 'important');
                bodyWidget.style.setProperty('left', 'auto', 'important');
                bodyWidget.style.setProperty('width', 'auto', 'important');
                bodyWidget.style.setProperty('height', 'auto', 'important');

                console.log('Chat widget repositionné avec succès');
            }
        }
    },

    // Fonction pour déplacer le widget vers le body
    moveToBody: function() {
        // Chercher tous les widgets possibles
        const widgets = document.querySelectorAll('.floating-chat-widget, #floating-chat-widget');

        widgets.forEach(widget => {
            if (widget.parentElement !== document.body) {
                // Cloner le widget pour préserver les event listeners Blazor
                const clonedWidget = widget.cloneNode(true);

                // Supprimer l'ancien
                widget.remove();

                // Ajouter au body
                document.body.appendChild(clonedWidget);

                console.log('Widget déplacé vers le body');
            }
        });

        // Appliquer les styles de positionnement
        this.ensureFloatingPosition();
    },

    // Observer les changements DOM pour maintenir la position
    observeChanges: function() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    // Vérifier si le widget existe et le repositionner si nécessaire
                    setTimeout(() => {
                        this.ensureFloatingPosition();
                    }, 100);
                }
            });
        });

        // Observer les changements dans le document
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    },

    // Initialiser le widget flottant
    init: function() {
        // Attendre que le DOM soit prêt
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupWidget();
            });
        } else {
            this.setupWidget();
        }
    },

    setupWidget: function() {
        // Fonction pour configurer le widget
        const setup = () => {
            this.moveToBody();
            this.ensureFloatingPosition();
        };

        // Configuration initiale
        setup();

        // Observer les changements
        this.observeChanges();

        // Vérifier très fréquemment la position (toutes les 500ms)
        setInterval(() => {
            this.ensureFloatingPosition();
        }, 500);

        // Vérifier aussi lors des événements de scroll et resize
        window.addEventListener('scroll', () => {
            this.ensureFloatingPosition();
        });

        window.addEventListener('resize', () => {
            this.ensureFloatingPosition();
        });

        // Vérifier lors des changements de route (pour les SPAs)
        window.addEventListener('popstate', () => {
            setTimeout(setup, 100);
        });
    }
};

// Fonction pour faire défiler vers le bas dans le chat
window.scrollToBottom = function(elementId) {
    const element = document.getElementById(elementId) || document.querySelector('.chat-messages');
    if (element) {
        element.scrollTop = element.scrollHeight;
    }
};

// Initialiser automatiquement
window.floatingChat.init();
