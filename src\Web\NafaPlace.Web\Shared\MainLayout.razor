@using NafaPlace.Web.Services
@using Microsoft.AspNetCore.Components.Authorization
@using NafaPlace.Web.Models.Catalog
@using NafaPlace.Web.Components.Search
@inherits LayoutComponentBase
@inject AuthenticationStateProvider AuthStateProvider
@inject ILocalStorageService LocalStorage
@inject NavigationManager NavigationManager
@inject IAuthService AuthService
@inject ICategoryService CategoryService
@inject IJSRuntime JSRuntime

<div class="page">
    <header class="header-blue">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="container">
                <div class="row">
                    <div class="col-md-8 top-contact">
                        <span><i class="bi bi-envelope"></i> <EMAIL></span>
                        <span>Livraison gratuite pour les commandes > 50.000 FCFA</span>
                    </div>
                    <div class="col-md-4 text-end top-links">
                        <AuthorizeView>
                            <Authorized>
                                <a href="/account/dashboard"><i class="bi bi-person"></i> Mon <PERSON></a>
                                <a href="/account/orders"><i class="bi bi-box"></i> Mes Commandes</a>
                                <a href="/loyalty/program"><i class="bi bi-gift text-warning"></i> Mes Points</a>
                                <a href="/track-delivery"><i class="bi bi-truck"></i> Suivi Livraison</a>
                                <a href="#" @onclick="Logout"><i class="bi bi-box-arrow-right"></i> Déconnexion</a>
                            </Authorized>
                            <NotAuthorized>
                                <a href="/auth/login"><i class="bi bi-box-arrow-in-right"></i> Connexion</a>
                                <a href="/auth/register"><i class="bi bi-person-plus"></i> Inscription</a>
                                <a href="/track-delivery"><i class="bi bi-truck"></i> Suivi Livraison</a>
                                <a href="#" @onclick='() => ChangeLanguage("fr")'><i class="bi bi-globe"></i> Français</a>
                            </NotAuthorized>
                        </AuthorizeView>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Header -->
        <div class="container">
            <div class="main-header">
                <!-- Menu button pour très petits écrans -->
                <button class="menu-btn d-lg-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#mobileMenu">
                    <i class="bi bi-list"></i>
                </button>
                
                <a href="/" class="logo">
                    <img src="images/nafaplace-gradient-modern.svg" alt="NafaPlace Logo" height="80" />
                </a>
                
                <div class="search-wrapper">
                    <SearchAutocomplete
                        Placeholder="Rechercher des produits..."
                        CssClass="search-input-modern"
                        ShowSearchButton="true" />
                </div>
                
                <div class="user-actions">
                    <AuthorizeView>
                        <Authorized>
                            <div class="dropdown">
                                <a href="#" class="action-item dropdown-toggle" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-person action-icon"></i>
                                    <span class="action-label">Compte</span>
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="userDropdown">
                                    <li><a class="dropdown-item" href="/account/dashboard"><i class="bi bi-speedometer2 me-2"></i>Tableau de bord</a></li>
                                    <li><a class="dropdown-item" href="/account/profile"><i class="bi bi-person-circle me-2"></i>Mon profil</a></li>
                                    <li><a class="dropdown-item" href="/account/orders"><i class="bi bi-bag me-2"></i>Mes commandes</a></li>
                                    <li><a class="dropdown-item" href="/account/my-reviews"><i class="bi bi-star me-2"></i>Mes avis</a></li>
                                    <li><a class="dropdown-item" href="/loyalty/program"><i class="bi bi-gift me-2"></i>Programme Fidélité</a></li>
                                    <li><a class="dropdown-item" href="/account/change-password"><i class="bi bi-key me-2"></i>Changer mot de passe</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" @onclick="Logout"><i class="bi bi-box-arrow-right me-2"></i>Déconnexion</a></li>
                                </ul>
                            </div>
                        </Authorized>
                        <NotAuthorized>
                            <a href="/auth/login" class="action-item">
                                <i class="bi bi-person action-icon"></i>
                                <span class="action-label">Connexion</span>
                            </a>
                        </NotAuthorized>
                    </AuthorizeView>
                    <NafaPlace.Web.Shared.Components.WishlistIndicator @ref="_wishlistIndicator" />
                    <NafaPlace.Web.Shared.Components.CartIndicator @ref="_cartIndicator" />
                </div>
            </div>
        </div>
        
        <nav class="navbar-blue">
            <div class="container navbar-container">
                <!-- Menu déroulant Toutes les Catégories -->
                <div class="dropdown">
                    <button class="categories-button dropdown-toggle" id="categoriesDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-grid"></i> Toutes les Catégories
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="categoriesDropdown">
                        @if (_categories != null && _categories.Any())
                        {
                            @foreach (var category in _categories)
                            {
                                <li><a class="dropdown-item" href="/catalog?category=@category.Id"><i class="bi bi-folder me-2"></i>@category.Name</a></li>
                            }
                            <li><hr class="dropdown-divider"></li>
                        }
                        <li><a class="dropdown-item" href="/catalog"><i class="bi bi-grid-3x3-gap me-2"></i>Toutes les catégories</a></li>
                    </ul>
                </div>
                
                <!-- Menu déroulant Nos Produits -->
                <div class="dropdown">
                    <button class="products-button dropdown-toggle" id="productsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-box-seam"></i> Nos Produits
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="productsDropdown">
                        <li><a class="dropdown-item" href="/catalog/featured"><i class="bi bi-star me-2"></i>Produits en vedette</a></li>
                        <li><a class="dropdown-item" href="/catalog/new"><i class="bi bi-gift me-2"></i>Nouveaux arrivages</a></li>
                        <li><a class="dropdown-item" href="/catalog/bestsellers"><i class="bi bi-award me-2"></i>Meilleures ventes</a></li>
                        <li><a class="dropdown-item" href="/catalog/discounts"><i class="bi bi-percent me-2"></i>Promotions</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/catalog"><i class="bi bi-box-seam me-2"></i>Tous les produits</a></li>
                    </ul>
                </div>

                <!-- Navigation principale -->
                <ul class="main-nav">
                    <li class="nav-item">
                        <a href="/" class="nav-link active">Accueil</a>
                    </li>
                    <li class="nav-item">
                        <a href="/nouveautes" class="nav-link">Nouveautés</a>
                    </li>
                    <li class="nav-item">
                        <a href="/promotions" class="nav-link">Promotions</a>
                    </li>

                    <!-- Menu Services -->
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" id="servicesDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            Services
                        </a>
                        <ul class="dropdown-menu services-dropdown" aria-labelledby="servicesDropdown">
                            <li>
                                <a class="dropdown-item" href="/recommendations">
                                    <i class="bi bi-robot text-primary me-2"></i>
                                    <div>
                                        <strong>Recommandations IA <span class="badge bg-success ms-1">Nouveau</span></strong>
                                        <small class="text-muted d-block">Produits personnalisés pour vous</small>
                                    </div>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/loyalty/program">
                                    <i class="bi bi-gift text-warning me-2"></i>
                                    <div>
                                        <strong>Programme Fidélité <span class="badge bg-warning text-dark ms-1">Populaire</span></strong>
                                        <small class="text-muted d-block">Gagnez des points et récompenses</small>
                                    </div>
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="/features/all">
                                    <i class="bi bi-grid-3x3-gap text-info me-2"></i>
                                    <div>
                                        <strong>Toutes les Fonctionnalités</strong>
                                        <small class="text-muted d-block">Découvrez tout ce que nous offrons</small>
                                    </div>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <li class="nav-item">
                        <a href="/meilleures-ventes" class="nav-link">Meilleures Ventes</a>
                    </li>
                    <li class="nav-item">
                        <a href="/blog" class="nav-link">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a href="/contact" class="nav-link">Contact</a>
                    </li>
                </ul>
                

            </div>
        </nav>
    </header>

    <!-- Mobile Menu -->
    <div class="offcanvas offcanvas-start" tabindex="-1" id="mobileMenu">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title">Menu</h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="offcanvas"></button>
        </div>
        <div class="offcanvas-body p-0">
            <div class="mobile-search position-relative">
                <i class="bi bi-search mobile-search-icon"></i>
                <input type="text" class="mobile-search-input" placeholder="Rechercher..." @bind="searchQuery" @onkeypress="OnSearchKeyPress">
            </div>
            <ul class="mobile-nav">
                <li class="mobile-nav-item">
                    <a href="/" class="mobile-nav-link">
                        <i class="bi bi-house mobile-nav-icon"></i> Accueil
                    </a>
                </li>

                <!-- Toutes les Catégories avec dropdown -->
                <li class="mobile-nav-item">
                    <div class="mobile-dropdown">
                        <button class="mobile-nav-link mobile-dropdown-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#mobileCategoriesCollapse" aria-expanded="false">
                            <i class="bi bi-grid mobile-nav-icon"></i> Toutes les Catégories
                            <i class="bi bi-chevron-down ms-auto"></i>
                        </button>
                        <div class="collapse" id="mobileCategoriesCollapse">
                            <div class="mobile-submenu">
                                @if (_categories != null)
                                {
                                    @foreach (var category in _categories)
                                    {
                                        <NavLink href="@($"/catalog?categoryId={category.Id}")" class="mobile-submenu-link" @onclick="CloseMobileMenu">
                                            <i class="bi bi-tag mobile-nav-icon"></i> @category.Name
                                        </NavLink>
                                    }
                                }
                                <NavLink href="/catalog" class="mobile-submenu-link text-primary" @onclick="CloseMobileMenu">
                                    <i class="bi bi-arrow-right mobile-nav-icon"></i> Voir tous les produits
                                </NavLink>
                            </div>
                        </div>
                    </div>
                </li>

                <!-- Nos Produits avec dropdown -->
                <li class="mobile-nav-item">
                    <div class="mobile-dropdown">
                        <button class="mobile-nav-link mobile-dropdown-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#mobileProductsCollapse" aria-expanded="false">
                            <i class="bi bi-box mobile-nav-icon"></i> Nos Produits
                            <i class="bi bi-chevron-down ms-auto"></i>
                        </button>
                        <div class="collapse" id="mobileProductsCollapse">
                            <div class="mobile-submenu">
                                <NavLink href="/catalog" class="mobile-submenu-link" @onclick="CloseMobileMenu">
                                    <i class="bi bi-grid mobile-nav-icon"></i> Tous les produits
                                </NavLink>
                                <NavLink href="/nouveautes" class="mobile-submenu-link" @onclick="CloseMobileMenu">
                                    <i class="bi bi-stars mobile-nav-icon"></i> Nouveautés
                                </NavLink>
                                <NavLink href="/meilleures-ventes" class="mobile-submenu-link" @onclick="CloseMobileMenu">
                                    <i class="bi bi-trophy mobile-nav-icon"></i> Meilleures ventes
                                </NavLink>
                                <NavLink href="/promotions" class="mobile-submenu-link" @onclick="CloseMobileMenu">
                                    <i class="bi bi-percent mobile-nav-icon"></i> Promotions
                                </NavLink>
                                @if (_categories != null)
                                {
                                    @foreach (var category in _categories.Take(5))
                                    {
                                        <NavLink href="@($"/catalog?categoryId={category.Id}")" class="mobile-submenu-link" @onclick="CloseMobileMenu">
                                            <i class="bi bi-tag mobile-nav-icon"></i> @category.Name
                                        </NavLink>
                                    }
                                }
                            </div>
                        </div>
                    </div>
                </li>

                <!-- Services avec dropdown -->
                <li class="mobile-nav-item">
                    <div class="mobile-dropdown">
                        <button class="mobile-nav-link mobile-dropdown-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#mobileServicesCollapse" aria-expanded="false">
                            <i class="bi bi-gear mobile-nav-icon"></i> Services
                            <i class="bi bi-chevron-down ms-auto"></i>
                        </button>
                        <div class="collapse" id="mobileServicesCollapse">
                            <div class="mobile-submenu">
                                <NavLink href="/recommendations" class="mobile-submenu-link" @onclick="CloseMobileMenu">
                                    <i class="bi bi-robot mobile-nav-icon"></i> Recommandations IA
                                </NavLink>
                                <NavLink href="/loyalty/program" class="mobile-submenu-link" @onclick="CloseMobileMenu">
                                    <i class="bi bi-gift mobile-nav-icon"></i> Programme Fidélité
                                </NavLink>
                                <NavLink href="/track-delivery" class="mobile-submenu-link" @onclick="CloseMobileMenu">
                                    <i class="bi bi-truck mobile-nav-icon"></i> Suivi Livraison
                                </NavLink>
                                <NavLink href="/features/all" class="mobile-submenu-link" @onclick="CloseMobileMenu">
                                    <i class="bi bi-grid-3x3-gap mobile-nav-icon"></i> Toutes les Fonctionnalités
                                </NavLink>
                                <NavLink href="/chat/support" class="mobile-submenu-link" @onclick="CloseMobileMenu">
                                    <i class="bi bi-chat-dots mobile-nav-icon"></i> Support Chat
                                </NavLink>
                            </div>
                        </div>
                    </div>
                </li>
                <li class="mobile-nav-item">
                    <NavLink href="/blog" class="mobile-nav-link" @onclick="CloseMobileMenu">
                        <i class="bi bi-journal-text mobile-nav-icon"></i> Blog
                    </NavLink>
                </li>
                <li class="mobile-nav-item">
                    <NavLink href="/contact" class="mobile-nav-link" @onclick="CloseMobileMenu">
                        <i class="bi bi-envelope mobile-nav-icon"></i> Contact
                    </NavLink>
                </li>
            </ul>
            <AuthorizeView>
                <Authorized>
                    <div class="p-3">
                        <div class="d-grid gap-2">
                            <NavLink href="/account/dashboard" class="btn btn-primary" @onclick="CloseMobileMenu">
                                <i class="bi bi-person-circle me-2"></i>Mon Compte
                            </NavLink>
                            <NavLink href="/account/profile" class="btn btn-outline-light" @onclick="CloseMobileMenu">
                                <i class="bi bi-person me-2"></i>Mon Profil
                            </NavLink>
                            <NavLink href="/account/orders" class="btn btn-outline-light" @onclick="CloseMobileMenu">
                                <i class="bi bi-box me-2"></i>Mes Commandes
                            </NavLink>
                            <NavLink href="/account/my-reviews" class="btn btn-outline-light" @onclick="CloseMobileMenu">
                                <i class="bi bi-star me-2"></i>Mes Avis
                            </NavLink>
                            <NavLink href="/account/change-password" class="btn btn-outline-light" @onclick="CloseMobileMenu">
                                <i class="bi bi-key me-2"></i>Changer Mot de Passe
                            </NavLink>
                            <button @onclick="LogoutAndCloseMenu" class="btn btn-danger">
                                <i class="bi bi-box-arrow-right me-2"></i>Déconnexion
                            </button>
                        </div>
                    </div>
                </Authorized>
                <NotAuthorized>
                    <div class="p-3">
                        <div class="d-grid gap-2">
                            <a href="/auth/login" class="btn btn-primary">
                                <i class="bi bi-box-arrow-in-right me-2"></i>Connexion
                            </a>
                            <a href="/auth/register" class="btn btn-outline-light">
                                <i class="bi bi-person-plus me-2"></i>Inscription
                            </a>
                        </div>
                    </div>
                </NotAuthorized>
            </AuthorizeView>
        </div>
    </div>

    <main>
        <article class="content px-4">
            @Body
        </article>
    </main>


</div>

@code {
    private IEnumerable<CategoryDto>? _categories;
    private NafaPlace.Web.Shared.Components.CartIndicator? _cartIndicator;
    private NafaPlace.Web.Shared.Components.WishlistIndicator? _wishlistIndicator;
    private string searchQuery = "";

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        await LoadCategoriesAsync();
    }

    private async Task LoadCategoriesAsync()
    {
        try
        {
            _categories = await CategoryService.GetAllCategoriesAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des catégories: {ex.Message}");
            _categories = new List<CategoryDto>();
        }
    }

    private async Task Logout()
    {
        await AuthService.Logout();
        NavigationManager.NavigateTo("/", true);
    }

    private void ChangeLanguage(string culture)
    {
        // Logique pour changer la langue
    }

    private void OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter" && !string.IsNullOrWhiteSpace(searchQuery))
        {
            NavigationManager.NavigateTo($"/catalog?search={Uri.EscapeDataString(searchQuery)}");
        }
    }

    private async Task CloseMobileMenu()
    {
        // Fermer le menu mobile avec JavaScript
        await JSRuntime.InvokeVoidAsync("closeMobileMenu");
    }

    private async Task LogoutAndCloseMenu()
    {
        await CloseMobileMenu();
        await Logout();
    }
}

<!-- Chat Widget Flottant -->
<FloatingChatWidget />
