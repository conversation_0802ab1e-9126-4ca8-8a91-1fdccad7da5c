using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using Blazored.LocalStorage;
using NafaPlace.Web;
using NafaPlace.Web.Services;

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

// Configuration des HttpClient pour chaque API
var identityApiUrl = builder.Configuration["ApiEndpoints:IdentityApi"] ?? "http://localhost:5155";
var catalogApiUrl = builder.Configuration["ApiEndpoints:CatalogApi"] ?? "http://localhost:5243";
var cartApiUrl = builder.Configuration["ApiEndpoints:CartApi"] ?? "http://localhost:5003"; // Port for the new Cart API
var orderApiUrl = builder.Configuration["ApiEndpoints:OrderApi"] ?? "http://localhost:5004"; // Port for the new Order API
var reviewApiUrl = builder.Configuration["ApiEndpoints:ReviewApi"] ?? "http://localhost:5006"; // Port for the Reviews API
var deliveryApiUrl = builder.Configuration["ApiEndpoints:DeliveryApi"] ?? "http://localhost:5010"; // Port for the Delivery API

// Service d'authentification centralisé pour HttpClient
builder.Services.AddScoped<IAuthenticatedHttpClientService, AuthenticatedHttpClientService>();

// HttpClient pour l'API Identity (utilisé par AuthService)
builder.Services.AddScoped<HttpClient>(sp => new HttpClient { BaseAddress = new Uri(identityApiUrl) });

// HttpClient pour l'API Catalog (utilisé par ProductService et CategoryService)
builder.Services.AddScoped<ICategoryService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(catalogApiUrl) };
    return new CategoryService(httpClient);
});

builder.Services.AddScoped<IProductService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(catalogApiUrl) };
    var reviewService = sp.GetRequiredService<IReviewService>();
    return new ProductService(httpClient, reviewService);
});

builder.Services.AddScoped<IAdvancedSearchService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(catalogApiUrl) };
    var logger = sp.GetRequiredService<ILogger<AdvancedSearchService>>();
    return new AdvancedSearchService(httpClient, logger);
});

// Services
builder.Services.AddBlazoredLocalStorage();
builder.Services.AddAuthorizationCore();

// Enregistrer CustomAuthStateProvider en premier
builder.Services.AddScoped<CustomAuthStateProvider>();
builder.Services.AddScoped<AuthenticationStateProvider>(provider =>
    provider.GetRequiredService<CustomAuthStateProvider>());

// Puis enregistrer les services
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddSingleton<TokenExpirationService>();
builder.Services.AddSingleton<CartNotificationService>();
builder.Services.AddScoped<ICartService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(cartApiUrl) };
    var notificationService = sp.GetRequiredService<CartNotificationService>();
    return new CartService(httpClient, notificationService);
});
builder.Services.AddScoped<IGuestCartMergeService, GuestCartMergeService>();

builder.Services.AddScoped<IOrderService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(orderApiUrl) };
    return new OrderService(httpClient);
});

builder.Services.AddScoped<IReviewService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(reviewApiUrl) };
    var authStateProvider = sp.GetRequiredService<AuthenticationStateProvider>();
    var localStorage = sp.GetRequiredService<ILocalStorageService>();
    var jsRuntime = sp.GetRequiredService<IJSRuntime>();
    return new ReviewService(httpClient, authStateProvider, localStorage, jsRuntime);
});

// Service Wishlist avec authentification centralisée
builder.Services.AddScoped<IWishlistService, WishlistService>();

// Service de coupons
builder.Services.AddScoped<ICouponService>(sp =>
{
    var httpClient = new HttpClient();
    var configuration = sp.GetRequiredService<IConfiguration>();
    var logger = sp.GetRequiredService<ILogger<CouponService>>();
    var jsRuntime = sp.GetRequiredService<IJSRuntime>();
    return new CouponService(httpClient, configuration, logger, jsRuntime);
});

// Service de suivi de livraison
builder.Services.AddScoped<IDeliveryTrackingService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(deliveryApiUrl) };
    var logger = sp.GetRequiredService<ILogger<DeliveryTrackingService>>();
    return new DeliveryTrackingService(httpClient, logger);
});

// Service JSRuntime sécurisé
builder.Services.AddScoped<ISafeJSRuntimeService, SafeJSRuntimeService>();

var app = builder.Build();

// Démarrer le service de vérification d'expiration des tokens
var tokenExpirationService = app.Services.GetRequiredService<TokenExpirationService>();
tokenExpirationService.StartTokenExpirationCheck();

await app.RunAsync();
